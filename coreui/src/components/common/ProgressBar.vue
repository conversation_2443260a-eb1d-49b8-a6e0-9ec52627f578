<template>
  <BaseModal
    :show="dialog"
    :title="progressData.message || 'Loading...'"
    color="primary"
    :width="options.width"
    :z-index="options.zIndex"
    :close-on-overlay="false"
    :closable="false"
    :manage-app-state="false"
  >
    <template #icon>
      <div class="spinner" v-if="progressData.progress === 0"></div>
      <svg v-else class="checkmark-icon" viewBox="0 0 24 24" width="24" height="24">
        <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
        <path d="M8 12l2 2 4-4" fill="none" stroke="currentColor" stroke-width="2"/>
      </svg>
    </template>

    <template #content>
      <!-- Progress Bar Container -->
      <div class="progress-bar-container">
        <div
          class="progress-bar-track"
          :class="{ 'progress-bar-track--dark': $store.state.darkMode }"
        >
          <div
            class="progress-bar-fill"
            :class="{
              'indeterminate': progressData.progress === 0,
              'progress-bar-fill--dark': $store.state.darkMode
            }"
            :style="{ width: progressData.progress > 0 ? progressData.progress + '%' : '0%' }"
          >
            <div class="progress-bar-shine"></div>
          </div>
        </div>
      </div>

      <!-- Progress Text -->
      <div class="progress-text">
        <div
          class="progress-percentage"
          :class="{ 'progress-percentage--dark': $store.state.darkMode }"
          v-if="progressData.progress > 0"
        >
          {{ Math.ceil(progressData.progress) }}%
        </div>
        <div
          class="progress-status"
          :class="{ 'progress-status--dark': $store.state.darkMode }"
          v-if="progressData.progress > 0"
        >
          {{ Math.ceil(progressData.progress) }}% complete
        </div>
        <div
          class="progress-status"
          :class="{ 'progress-status--dark': $store.state.darkMode }"
          v-else
        >
          Processing...
        </div>
      </div>

      <!-- Additional Info -->
      <div
        class="progress-info"
        :class="{ 'progress-info--dark': $store.state.darkMode }"
        v-if="progressData.estimatedTime || progressData.elapsedTime"
      >
        <div class="time-info">
          <span
            v-if="progressData.elapsedTime"
            class="elapsed-time"
            :class="{ 'elapsed-time--dark': $store.state.darkMode }"
          >
            Elapsed: {{ formatTime(progressData.elapsedTime) }}
          </span>
          <span
            v-if="progressData.estimatedTime"
            class="estimated-time"
            :class="{ 'estimated-time--dark': $store.state.darkMode }"
          >
            Remaining: {{ formatTime(progressData.estimatedTime) }}
          </span>
        </div>
      </div>
    </template>
  </BaseModal>
</template>


<script>
import { mapState, mapActions } from "vuex";
import BaseModal from "./BaseModal.vue";

export default {
  components: {
    BaseModal
  },
  data: () => ({
    dialog: false,
    resolve: null,
    reject: null,
    options: {
      color: "primary",
      width: 350, // Increased width to accommodate progress text
      zIndex: 1000000000,
    },
  }),
  computed:{
    ...mapState("app", ["progressBar", "progressData"])
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(options) {
      this.loadVApp()
      this.dialog = true;
      this.options = Object.assign(this.options, options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      })
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp()
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp()
    },
    formatTime(seconds) {
      if (!seconds || seconds < 0) return '0s';

      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      if (hours > 0) {
        return `${hours}h ${minutes}m ${secs}s`;
      } else if (minutes > 0) {
        return `${minutes}m ${secs}s`;
      } else {
        return `${secs}s`;
      }
    },
  },
  watch:{
    progressBar(newVal) {
      this.dialog = newVal
      if(newVal) {
        this.loadVApp()
        return
      }
      this.unLoadVApp()
    }
  },
};
</script>

<style scoped>
/* Spinner animation */
.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.checkmark-icon {
  color: white;
  animation: checkmarkPulse 0.6s ease-out;
}

@keyframes checkmarkPulse {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Progress Bar */
.progress-bar-container {
  margin-bottom: 20px;
}

.progress-bar-track {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Dark theme track */
.progress-bar-track--dark {
  background: #4a5568;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  border-radius: 4px;
  position: relative;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1), background 0.3s ease;
  overflow: hidden;
}

/* Dark theme fill */
.progress-bar-fill--dark {
  background: linear-gradient(90deg, #48bb78, #38b2ac);
}

.progress-bar-fill.indeterminate {
  width: 100% !important;
  background: linear-gradient(90deg, #667eea, #764ba2);
  animation: indeterminate 2s ease-in-out infinite;
}

/* Dark theme indeterminate */
.progress-bar-fill--dark.indeterminate {
  background: linear-gradient(90deg, #4a5568, #2d3748);
}

@keyframes indeterminate {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-bar-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Progress Text */
.progress-text {
  text-align: center;
  margin-bottom: 16px;
}

.progress-percentage {
  font-size: 32px;
  font-weight: 700;
  color: #28a745;
  margin-bottom: 8px;
  animation: countUp 0.6s ease-out;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: color 0.3s ease, text-shadow 0.3s ease;
}

/* Dark theme percentage */
.progress-percentage--dark {
  color: #48bb78;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

@keyframes countUp {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.progress-status {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
  transition: color 0.3s ease;
}

/* Dark theme status */
.progress-status--dark {
  color: #a0aec0;
}

/* Progress Info */
.progress-info {
  border-top: 1px solid #e9ecef;
  padding-top: 16px;
  margin-top: 16px;
  transition: border-color 0.3s ease;
}

/* Dark theme info */
.progress-info--dark {
  border-top-color: #4a5568;
}

.time-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #6c757d;
  transition: color 0.3s ease;
}

.elapsed-time,
.estimated-time {
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  font-weight: 500;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.elapsed-time {
  color: #17a2b8;
}

.estimated-time {
  color: #fd7e14;
}

/* Dark theme time badges */
.elapsed-time--dark,
.estimated-time--dark {
  background: #4a5568;
  color: #e2e8f0;
}

.elapsed-time--dark {
  color: #63b3ed;
}

.estimated-time--dark {
  color: #f6ad55;
}

/* Responsive design for progress elements */
@media (max-width: 480px) {
  .progress-percentage {
    font-size: 28px;
  }

  .time-info {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
}

/* High contrast mode for progress elements */
@media (prefers-contrast: high) {
  .progress-bar-fill {
    background: #000 !important;
  }

  .progress-bar-fill--dark {
    background: #fff !important;
  }

  .progress-percentage {
    color: #000 !important;
  }

  .progress-percentage--dark {
    color: #fff !important;
  }
}

/* Reduced motion for progress elements */
@media (prefers-reduced-motion: reduce) {
  .progress-bar-fill,
  .progress-percentage,
  .checkmark-icon,
  .spinner {
    animation: none !important;
  }

  .progress-bar-fill {
    transition: width 0.3s ease !important;
  }
}
</style>
