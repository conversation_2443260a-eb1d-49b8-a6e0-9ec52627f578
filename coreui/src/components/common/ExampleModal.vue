<template>
  <BaseModal
    :show="show"
    :title="title"
    :color="color"
    :width="width"
    :closable="closable"
    :close-on-overlay="closeOnOverlay"
    @close="$emit('close')"
  >
    <!-- Custom icon slot -->
    <template #icon v-if="icon">
      <component :is="icon" />
    </template>

    <!-- Content slot -->
    <template #content>
      <slot name="content">
        <p>{{ message }}</p>
      </slot>
    </template>

    <!-- Footer slot with buttons -->
    <template #footer v-if="showFooter">
      <slot name="footer">
        <div class="modal-actions">
          <button
            v-if="showCancel"
            @click="$emit('cancel')"
            class="modal-btn modal-btn--cancel"
            :class="{ 'modal-btn--cancel--dark': $store.state.darkMode }"
          >
            {{ cancelText }}
          </button>
          <button
            v-if="showConfirm"
            @click="$emit('confirm')"
            class="modal-btn modal-btn--confirm"
            :class="[
              `modal-btn--${color}`,
              { [`modal-btn--${color}--dark`]: $store.state.darkMode }
            ]"
          >
            {{ confirmText }}
          </button>
        </div>
      </slot>
    </template>
  </BaseModal>
</template>

<script>
import BaseModal from "./BaseModal.vue";

export default {
  name: 'ExampleModal',
  components: {
    BaseModal
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'Modal Title'
    },
    message: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: 'primary'
    },
    width: {
      type: Number,
      default: 400
    },
    closable: {
      type: Boolean,
      default: true
    },
    closeOnOverlay: {
      type: Boolean,
      default: true
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    showCancel: {
      type: Boolean,
      default: true
    },
    showConfirm: {
      type: Boolean,
      default: true
    },
    cancelText: {
      type: String,
      default: 'Cancel'
    },
    confirmText: {
      type: String,
      default: 'Confirm'
    },
    icon: {
      type: [String, Object],
      default: null
    }
  },
  emits: ['close', 'cancel', 'confirm']
};
</script>

<style scoped>
/* Modal Actions */
.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.modal-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.modal-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-btn:active {
  transform: translateY(0);
}

/* Cancel Button */
.modal-btn--cancel {
  background: #6c757d;
  color: white;
}

.modal-btn--cancel:hover {
  background: #5a6268;
}

.modal-btn--cancel--dark {
  background: #4a5568;
  color: #e2e8f0;
}

.modal-btn--cancel--dark:hover {
  background: #2d3748;
}

/* Primary Button */
.modal-btn--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-btn--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.modal-btn--primary--dark {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
}

.modal-btn--primary--dark:hover {
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
}

/* Danger Button */
.modal-btn--danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
}

.modal-btn--danger:hover {
  background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
}

.modal-btn--danger--dark {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
}

.modal-btn--danger--dark:hover {
  background: linear-gradient(135deg, #c53030 0%, #9c2626 100%);
}

/* Success Button */
.modal-btn--success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.modal-btn--success:hover {
  background: linear-gradient(135deg, #218838 0%, #1ba085 100%);
}

.modal-btn--success--dark {
  background: linear-gradient(135deg, #48bb78 0%, #38b2ac 100%);
}

.modal-btn--success--dark:hover {
  background: linear-gradient(135deg, #38a169 0%, #319795 100%);
}

/* Warning Button */
.modal-btn--warning {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  color: #212529;
}

.modal-btn--warning:hover {
  background: linear-gradient(135deg, #e0a800 0%, #e8690b 100%);
}

.modal-btn--warning--dark {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
  color: white;
}

.modal-btn--warning--dark:hover {
  background: linear-gradient(135deg, #dd6b20 0%, #c05621 100%);
}

/* Info Button */
.modal-btn--info {
  background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
  color: white;
}

.modal-btn--info:hover {
  background: linear-gradient(135deg, #138496 0%, #5a32a3 100%);
}

.modal-btn--info--dark {
  background: linear-gradient(135deg, #4299e1 0%, #553c9a 100%);
}

.modal-btn--info--dark:hover {
  background: linear-gradient(135deg, #3182ce 0%, #44337a 100%);
}

/* Button shine effect */
.modal-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.modal-btn:hover::before {
  left: 100%;
}

/* Responsive design */
@media (max-width: 480px) {
  .modal-actions {
    flex-direction: column;
    gap: 8px;
  }

  .modal-btn {
    width: 100%;
  }
}

/* Focus states for accessibility */
.modal-btn:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}
</style>
