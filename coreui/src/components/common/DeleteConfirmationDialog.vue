<template>
  <div
    v-if="dialog"
    class="delete-overlay"
    :class="{ 'delete-overlay--dark': $store.state.darkMode }"
    :style="{ zIndex: options.zIndex }"
  >
    <div
      class="delete-modal"
      :class="{ 'delete-modal--dark': $store.state.darkMode }"
      :style="{ maxWidth: options.width + 'px' }"
    >
      <!-- Header with gradient background -->
      <div
        class="delete-header"
        :class="{ 'delete-header--dark': $store.state.darkMode }"
      >
        <div class="delete-icon">
          <svg class="warning-icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M12 2L1 21h22L12 2zm0 3.99L19.53 19H4.47L12 5.99zM11 16h2v2h-2v-2zm0-6h2v4h-2v-4z" fill="currentColor"/>
          </svg>
        </div>
        <h4 class="delete-title">
          Delete Confirmation
        </h4>
      </div>

      <!-- Content Section -->
      <div
        class="delete-content"
        :class="{ 'delete-content--dark': $store.state.darkMode }"
      >
        <!-- Message -->
        <div class="delete-message">
          <p class="delete-text" :class="{ 'delete-text--dark': $store.state.darkMode }">
            {{ confirmDeleteMsg || 'Are you sure you want to delete this item?' }}
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="delete-actions">
          <button
            @click="close"
            class="delete-btn delete-btn--cancel"
            :class="{ 'delete-btn--cancel--dark': $store.state.darkMode }"
          >
            Cancel
          </button>
          <button
            @click="deleteItem"
            class="delete-btn delete-btn--delete"
            :class="{ 'delete-btn--delete--dark': $store.state.darkMode }"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from "vuex";
export default {
  data() {
    return {
      dialog: false,
      confirmDeleteMsg: localStorage.getItem("confirm_delete"),
      deletedItem: null,
      options: {
        width: 400,
        zIndex: 1000000000,
      },
    };
  },
  computed: {
    ...mapState("app", [])
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open() {
      this.loadVApp();
      this.dialog = true;
    },
    close() {
      this.resolve && this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    deleteItem() {
      this.$root.$emit("DeleteConfirmed", this.deletedItem);
      this.close();
    }
  },
  mounted() {
    this.$root.$on('alertDeleteConfirm', (item) => {
      this.deletedItem = item;
      this.open();
    });
  },
};
</script>

<style scoped>
/* ==============================================
   LIGHT THEME (DEFAULT)
   ============================================== */

/* Overlay */
.delete-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
  transition: background-color 0.3s ease;
}

/* Dark theme overlay */
.delete-overlay--dark {
  background: rgba(0, 0, 0, 0.8);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modal */
.delete-modal {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  min-width: 400px;
  max-width: 90vw;
  animation: slideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Dark theme modal */
.delete-modal--dark {
  background: #2d3748;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
  color: #e2e8f0;
}

@keyframes slideIn {
  from {
    transform: translateY(-50px) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Header */
.delete-header {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  padding: 20px 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  overflow: hidden;
  transition: background 0.3s ease;
}

/* Dark theme header */
.delete-header--dark {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
}

.delete-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 2s infinite;
}

/* Dark theme shimmer */
.delete-header--dark::before {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.delete-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.warning-icon {
  color: white;
  animation: warningPulse 2s ease-in-out infinite;
}

@keyframes warningPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.delete-title {
  color: white;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Content */
.delete-content {
  padding: 32px 24px 24px;
  transition: background-color 0.3s ease;
}

/* Dark theme content */
.delete-content--dark {
  background-color: #2d3748;
}

/* Message */
.delete-message {
  margin-bottom: 24px;
  text-align: center;
}

.delete-text {
  font-size: 16px;
  color: #495057;
  margin: 0;
  line-height: 1.5;
  transition: color 0.3s ease;
}

/* Dark theme text */
.delete-text--dark {
  color: #e2e8f0;
}

/* Action Buttons */
.delete-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.delete-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.delete-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.delete-btn:active {
  transform: translateY(0);
}

/* Cancel Button */
.delete-btn--cancel {
  background: #6c757d;
  color: white;
}

.delete-btn--cancel:hover {
  background: #5a6268;
}

/* Dark theme cancel button */
.delete-btn--cancel--dark {
  background: #4a5568;
  color: #e2e8f0;
}

.delete-btn--cancel--dark:hover {
  background: #2d3748;
}

/* Delete Button */
.delete-btn--delete {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
}

.delete-btn--delete:hover {
  background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
}

/* Dark theme delete button */
.delete-btn--delete--dark {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
}

.delete-btn--delete--dark:hover {
  background: linear-gradient(135deg, #c53030 0%, #9c2626 100%);
}

/* Button shine effect */
.delete-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.delete-btn:hover::before {
  left: 100%;
}

/* ==============================================
   RESPONSIVE DESIGN
   ============================================== */

@media (max-width: 480px) {
  .delete-modal {
    min-width: 320px;
    margin: 16px;
  }

  .delete-header {
    padding: 16px 20px;
  }

  .delete-title {
    font-size: 16px;
  }

  .delete-content {
    padding: 24px 20px 20px;
  }

  .delete-actions {
    flex-direction: column;
    gap: 8px;
  }

  .delete-btn {
    width: 100%;
  }
}

/* ==============================================
   ACCESSIBILITY & PREFERENCES
   ============================================== */

/* High contrast mode */
@media (prefers-contrast: high) {
  .delete-header {
    background: #000 !important;
  }

  .delete-header--dark {
    background: #fff !important;
    color: #000 !important;
  }

  .delete-btn--delete {
    background: #000 !important;
  }

  .delete-btn--delete--dark {
    background: #fff !important;
    color: #000 !important;
  }

  .delete-btn--cancel {
    background: #666 !important;
  }

  .delete-btn--cancel--dark {
    background: #ccc !important;
    color: #000 !important;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .delete-overlay,
  .delete-modal,
  .warning-icon,
  .delete-header::before {
    animation: none !important;
  }

  * {
    transition-duration: 0.1s !important;
  }
}

/* ==============================================
   THEME UTILITIES
   ============================================== */

/* Smooth theme transitions for all elements */
.delete-overlay,
.delete-modal,
.delete-header,
.delete-content,
.delete-text,
.delete-btn {
  transition-property: background-color, color, border-color, box-shadow, text-shadow;
  transition-duration: 0.3s;
  transition-timing-function: ease;
}

/* Focus states for accessibility */
.delete-modal:focus-within {
  outline: 2px solid #dc3545;
  outline-offset: 2px;
}

.delete-modal--dark:focus-within {
  outline-color: #e53e3e;
}

.delete-btn:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .delete-overlay {
    display: none !important;
  }
}
</style>
