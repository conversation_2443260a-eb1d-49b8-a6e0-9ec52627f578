<template>
  <BaseModal
    :show="dialog"
    title="Delete Confirmation"
    color="danger"
    :width="options.width"
    :z-index="options.zIndex"
    :close-on-overlay="false"
    :closable="false"
    @close="close"
  >
    <template #icon>
      <svg class="warning-icon" viewBox="0 0 24 24" width="24" height="24">
        <path d="M12 2L1 21h22L12 2zm0 3.99L19.53 19H4.47L12 5.99zM11 16h2v2h-2v-2zm0-6h2v4h-2v-4z" fill="currentColor"/>
      </svg>
    </template>

    <template #content>
      <!-- Message -->
      <div class="delete-message">
        <p class="delete-text" :class="{ 'delete-text--dark': $store.state.darkMode }">
          {{ confirmDeleteMsg || 'Are you sure you want to delete this item?' }}
        </p>
      </div>

      <!-- Action Buttons -->
      <div class="delete-actions">
        <button
          @click="close"
          class="delete-btn delete-btn--cancel"
          :class="{ 'delete-btn--cancel--dark': $store.state.darkMode }"
        >
          Cancel
        </button>
        <button
          @click="deleteItem"
          class="delete-btn delete-btn--delete"
          :class="{ 'delete-btn--delete--dark': $store.state.darkMode }"
        >
          Delete
        </button>
      </div>
    </template>
  </BaseModal>
</template>

<script>
import { mapState, mapActions } from "vuex";
import BaseModal from "./BaseModal.vue";

export default {
  components: {
    BaseModal
  },
  data() {
    return {
      dialog: false,
      confirmDeleteMsg: localStorage.getItem("confirm_delete"),
      deletedItem: null,
      options: {
        width: 400,
        zIndex: 1000000000,
      },
    };
  },
  computed: {
    ...mapState("app", [])
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open() {
      this.loadVApp();
      this.dialog = true;
    },
    close() {
      this.resolve && this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    deleteItem() {
      this.$root.$emit("DeleteConfirmed", this.deletedItem);
      this.close();
    }
  },
  mounted() {
    this.$root.$on('alertDeleteConfirm', (item) => {
      this.deletedItem = item;
      this.open();
    });
  },
};
</script>

<style scoped>
/* Warning icon animation */
.warning-icon {
  color: white;
  animation: warningPulse 2s ease-in-out infinite;
}

@keyframes warningPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Message */
.delete-message {
  margin-bottom: 24px;
  text-align: center;
}

.delete-text {
  font-size: 16px;
  color: #495057;
  margin: 0;
  line-height: 1.5;
  transition: color 0.3s ease;
}

/* Dark theme text */
.delete-text--dark {
  color: #e2e8f0;
}

/* Action Buttons */
.delete-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.delete-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.delete-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.delete-btn:active {
  transform: translateY(0);
}

/* Cancel Button */
.delete-btn--cancel {
  background: #6c757d;
  color: white;
}

.delete-btn--cancel:hover {
  background: #5a6268;
}

/* Dark theme cancel button */
.delete-btn--cancel--dark {
  background: #4a5568;
  color: #e2e8f0;
}

.delete-btn--cancel--dark:hover {
  background: #2d3748;
}

/* Delete Button */
.delete-btn--delete {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
}

.delete-btn--delete:hover {
  background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
}

/* Dark theme delete button */
.delete-btn--delete--dark {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
}

.delete-btn--delete--dark:hover {
  background: linear-gradient(135deg, #c53030 0%, #9c2626 100%);
}

/* Button shine effect */
.delete-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.delete-btn:hover::before {
  left: 100%;
}

/* Responsive design for buttons */
@media (max-width: 480px) {
  .delete-actions {
    flex-direction: column;
    gap: 8px;
  }

  .delete-btn {
    width: 100%;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .warning-icon {
    animation: none !important;
  }

  .delete-btn {
    transition-duration: 0.1s !important;
  }
}

/* Focus states for accessibility */
.delete-btn:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}
</style>
