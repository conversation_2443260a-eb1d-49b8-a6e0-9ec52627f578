<template>
  <div
    v-if="show"
    class="base-overlay"
    :class="{ 'base-overlay--dark': $store.state.darkMode }"
    :style="{ zIndex: zIndex }"
    @click.self="handleOverlayClick"
  >
    <div
      class="base-modal"
      :class="{ 'base-modal--dark': $store.state.darkMode }"
      :style="{ maxWidth: width + 'px' }"
    >
      <!-- Header with gradient background -->
      <div
        class="base-header"
        :class="[
          { 'base-header--dark': $store.state.darkMode },
          `base-header--${color}`
        ]"
      >
        <div class="base-icon" v-if="$slots.icon">
          <slot name="icon"></slot>
        </div>
        <h4 class="base-title">
          <slot name="title">{{ title }}</slot>
        </h4>
        <button
          v-if="closable"
          @click="$emit('close')"
          class="base-close"
          :class="{ 'base-close--dark': $store.state.darkMode }"
        >
          <svg viewBox="0 0 24 24" width="20" height="20">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"/>
          </svg>
        </button>
      </div>

      <!-- Content Section -->
      <div
        class="base-content"
        :class="{ 'base-content--dark': $store.state.darkMode }"
      >
        <slot name="content"></slot>
      </div>

      <!-- Footer Section -->
      <div
        v-if="$slots.footer"
        class="base-footer"
        :class="{ 'base-footer--dark': $store.state.darkMode }"
      >
        <slot name="footer"></slot>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  name: 'BaseModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: 'primary',
      validator: value => ['primary', 'danger', 'success', 'warning', 'info'].includes(value)
    },
    width: {
      type: Number,
      default: 400
    },
    zIndex: {
      type: Number,
      default: 1000000000
    },
    closable: {
      type: Boolean,
      default: true
    },
    closeOnOverlay: {
      type: Boolean,
      default: true
    },
    manageAppState: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    ...mapState("app", [])
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    handleOverlayClick() {
      if (this.closeOnOverlay) {
        this.$emit('close');
      }
    }
  },
  watch: {
    show(newVal) {
      if (this.manageAppState) {
        if (newVal) {
          this.loadVApp();
        } else {
          this.unLoadVApp();
        }
      }
    }
  }
};
</script>

<style scoped>
/* ==============================================
   LIGHT THEME (DEFAULT)
   ============================================== */

/* Overlay */
.base-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
  transition: background-color 0.3s ease;
}

/* Dark theme overlay */
.base-overlay--dark {
  background: rgba(0, 0, 0, 0.8);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modal */
.base-modal {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  min-width: 400px;
  max-width: 90vw;
  animation: slideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Dark theme modal */
.base-modal--dark {
  background: #2d3748;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
  color: #e2e8f0;
}

@keyframes slideIn {
  from {
    transform: translateY(-50px) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Header */
.base-header {
  padding: 20px 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  overflow: hidden;
  transition: background 0.3s ease;
}

/* Header color variants */
.base-header--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.base-header--danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.base-header--success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.base-header--warning {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.base-header--info {
  background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

/* Dark theme header variants */
.base-header--dark.base-header--primary {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
}

.base-header--dark.base-header--danger {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
}

.base-header--dark.base-header--success {
  background: linear-gradient(135deg, #48bb78 0%, #38b2ac 100%);
}

.base-header--dark.base-header--warning {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.base-header--dark.base-header--info {
  background: linear-gradient(135deg, #4299e1 0%, #553c9a 100%);
}

.base-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 2s infinite;
}

/* Dark theme shimmer */
.base-header--dark::before {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.base-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.base-title {
  color: white;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  flex: 1;
}

.base-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.base-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.base-close--dark:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* Content */
.base-content {
  padding: 32px 24px 24px;
  transition: background-color 0.3s ease;
}

/* Dark theme content */
.base-content--dark {
  background-color: #2d3748;
}

/* Footer */
.base-footer {
  padding: 16px 24px 24px;
  border-top: 1px solid #e9ecef;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

/* Dark theme footer */
.base-footer--dark {
  background-color: #2d3748;
  border-top-color: #4a5568;
}

/* ==============================================
   RESPONSIVE DESIGN
   ============================================== */

@media (max-width: 480px) {
  .base-modal {
    min-width: 320px;
    margin: 16px;
  }

  .base-header {
    padding: 16px 20px;
  }

  .base-title {
    font-size: 16px;
  }

  .base-content {
    padding: 24px 20px 20px;
  }

  .base-footer {
    padding: 12px 20px 20px;
  }
}

/* ==============================================
   ACCESSIBILITY & PREFERENCES
   ============================================== */

/* High contrast mode */
@media (prefers-contrast: high) {
  .base-header--primary {
    background: #000 !important;
  }

  .base-header--danger {
    background: #8b0000 !important;
  }

  .base-header--success {
    background: #006400 !important;
  }

  .base-header--warning {
    background: #ff8c00 !important;
  }

  .base-header--info {
    background: #000080 !important;
  }

  .base-header--dark {
    background: #fff !important;
    color: #000 !important;
  }

  .base-header--dark .base-title,
  .base-header--dark .base-close {
    color: #000 !important;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .base-overlay,
  .base-modal,
  .base-header::before {
    animation: none !important;
  }

  * {
    transition-duration: 0.1s !important;
  }
}

/* ==============================================
   THEME UTILITIES
   ============================================== */

/* Smooth theme transitions for all elements */
.base-overlay,
.base-modal,
.base-header,
.base-content,
.base-footer,
.base-close {
  transition-property: background-color, color, border-color, box-shadow, text-shadow;
  transition-duration: 0.3s;
  transition-timing-function: ease;
}

/* Focus states for accessibility */
.base-modal:focus-within {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.base-modal--dark:focus-within {
  outline-color: #63b3ed;
}

.base-close:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .base-overlay {
    display: none !important;
  }
}
</style>
